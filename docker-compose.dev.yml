version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: motion-agent-postgres
    environment:
      POSTGRES_DB: motion_agent
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d motion_agent"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - motion-agent-network

  # Redis 缓存和任务队列
  redis:
    image: redis:7-alpine
    container_name: motion-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - motion-agent-network

  # Redis Commander (Redis 管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: motion-agent-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - motion-agent-network

  # pgAdmin (PostgreSQL 管理界面)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: motion-agent-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - motion-agent-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  motion-agent-network:
    driver: bridge
