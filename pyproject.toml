[project]
name = "motion-agent"
version = "0.1.0"
description = "Natural Language to 3D Animation Generation System"
readme = "README.md"
requires-python = ">=3.11"
authors = [{ name = "BigFaceMaster", email = "<EMAIL>" }]
dependencies = [
  # FastAPI and web framework
  "fastapi>=0.104.1",
  "uvicorn[standard]>=0.24.0",
  "pydantic>=2.5.0",
  "python-multipart>=0.0.6",
  "python-dotenv>=1.0.0",
  "aiofiles>=23.2.1",
  "jinja2>=3.1.2",
  "requests>=2.31.0",
  # Logging
  "loguru>=0.7.2",
  # Task queue and async processing
  "taskiq>=0.11.17",
  "taskiq-redis>=1.0.8",
  # Database
  "sqlalchemy>=2.0.0",
  "alembic>=1.13.0",
  "asyncpg>=0.29.0",
  "psycopg2-binary>=2.9.0",
  # NLU and AI dependencies
  "transformers>=4.35.0",
  "torch>=2.1.0",
  "numpy>=1.24.0",
  "langchain>=0.0.350",
  "langgraph>=0.0.40",
  # Animation and math
  "scipy>=1.11.0",
  "matplotlib>=3.7.0",
  "nltk>=3.9.1",
  "haystack-ai>=1.0.0",
  "spacy>=3.8.7",
]

[project.optional-dependencies]
blender = ["bpy>=4.0.0", "mathutils>=1.0.0"]

[dependency-groups]
dev = [
  "black>=23.11.0",
  "flake8>=6.1.0",
  "mypy>=1.7.1",
  "pre-commit>=3.5.0",
  "ruff>=0.1.8",
]

[project.scripts]
start-backend = "start_backend:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["backend"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | .history
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
mypy_path = "stubs"

# Ignore missing imports for Blender modules when not in Blender environment
[[tool.mypy.overrides]]
module = "bpy"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "mathutils"
ignore_missing_imports = true

