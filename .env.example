# 专业游戏动画师系统配置
# Professional Game Animator System Configuration with Taskiq

# API配置
API_HOST=0.0.0.0
API_PORT=9000
DEBUG=true

# Database Configuration
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/motion_agent
DATABASE_URL_SYNC=postgresql://user:password@localhost:5432/motion_agent

# Redis Configuration (for Taskiq)
REDIS_URL=redis://localhost:6379/0
REDIS_RESULT_URL=redis://localhost:6379/1

# Blender配置
BLENDER_PATH=/Applications/Blender.app/Contents/MacOS/Blender
# BLENDER_PATH=/usr/bin/blender  # Linux
# BLENDER_PATH=C:\Program Files\Blender Foundation\Blender\blender.exe  # Windows

# 角色模型配置
DEFAULT_CHARACTER_MODEL=models/default_character.blend
CHARACTER_MODELS_DIR=models/characters/

# 动画设置
DEFAULT_FRAME_RATE=30
DEFAULT_ANIMATION_QUALITY=game_ready
DEFAULT_EXPORT_FORMAT=fbx

# 输出配置
OUTPUT_DIR=output/animations
TEMP_DIR=temp/animation_data
LOG_DIR=logs

# 日志配置
LOG_LEVEL=INFO
LOG_ROTATION=10 MB
LOG_RETENTION=7 days
LOG_FORMAT="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"

# NLU配置
SPACY_MODEL=zh_core_web_sm
# SPACY_MODEL=en_core_web_sm  # 英文模型
TRANSFORMERS_MODEL=microsoft/DialoGPT-medium
HAYSTACK_ENABLED=true

# 动画师级别配置
DEFAULT_ANIMATOR_LEVEL=intermediate
ENABLE_JUNIOR_FUNCTIONS=true
ENABLE_INTERMEDIATE_FUNCTIONS=true
ENABLE_SENIOR_FUNCTIONS=false

# 性能配置
MAX_ANIMATION_DURATION=30.0
MAX_ACTIONS_PER_SEQUENCE=15
PROCESSING_TIMEOUT=300
BLENDER_TIMEOUT=300

# 质量配置
ENABLE_QUALITY_ASSESSMENT=true
ENABLE_OPTIMIZATION_SUGGESTIONS=true
COMPLEXITY_THRESHOLD=80

# 缓存配置
ENABLE_CACHING=true
CACHE_DIR=cache/
CACHE_EXPIRY=3600

# Task Queue Configuration
TASKIQ_WORKER_CONCURRENCY=4
TASKIQ_MAX_RETRIES=3
TASKIQ_RETRY_DELAY=60

# Security Configuration (for production)
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# External Services (if needed)
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key

# 开发配置
ENABLE_PROFILING=false
ENABLE_METRICS=true
ENABLE_SWAGGER_UI=true
DEVELOPMENT_MODE=true
